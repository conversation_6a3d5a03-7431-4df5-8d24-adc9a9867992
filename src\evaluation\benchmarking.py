"""
Benchmarking and evaluation system for loan approval predictions.
"""

import time
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import pandas as pd
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score, confusion_matrix
import numpy as np

from ..models.loan_data import LoanData
from ..models.llm_response import LLMResponse, EvaluationResult, BatchEvaluationResult, ComparisonResult, LoanDecision
from ..approaches.base import LoanApprovalApproach


class LoanApprovalEvaluator:
    """Evaluator for loan approval prediction approaches."""

    def __init__(self):
        self.evaluation_history = []

    def evaluate_single_prediction(self,
                                   prediction: LLMResponse,
                                   actual_outcome: bool,
                                   loan_id: Optional[str] = None,
                                   conditional_threshold: float = 0.6) -> EvaluationResult:
        """
        Evaluate a single prediction against ground truth.

        Args:
            prediction: LLM prediction response
            actual_outcome: True if loan was good (fully paid/current), False if bad
            loan_id: Optional loan identifier
            conditional_threshold: Threshold for converting CONDITIONAL to APPROVE (default: 0.6)

        Returns:
            EvaluationResult with correctness metrics
        """
        predicted_approve: bool = self._convert_decision_to_boolean(
            prediction, conditional_threshold)

        is_correct: bool = predicted_approve == actual_outcome

        # Classify prediction type
        if predicted_approve and actual_outcome:
            prediction_type = "TP"
        elif not predicted_approve and not actual_outcome:
            prediction_type = "TN"
        elif predicted_approve and not actual_outcome:
            prediction_type = "FP"
        else:  # !predicted_approve && actual_outcome
            prediction_type = "FN"

        return EvaluationResult(
            predicted_decision=prediction.decision,
            actual_outcome=actual_outcome,
            is_correct=is_correct,
            prediction_type=prediction_type,
            confidence_level=prediction.confidence,
            risk_level=prediction.risk_assessment,
            loan_id=loan_id,
            approach_used=prediction.model_name or "Unknown"
        )

    def _convert_decision_to_boolean(self, prediction: LLMResponse, conditional_threshold: float = 0.6) -> bool:
        """
        Convert LLM decision to boolean, handling CONDITIONAL decisions.

        Args:
            prediction: LLM prediction response
            conditional_threshold: Threshold for converting CONDITIONAL to APPROVE

        Returns:
            Boolean indicating whether to approve the loan
        """
        if prediction.decision == LoanDecision.APPROVE:
            return True
        elif prediction.decision == LoanDecision.DENY:
            return False
        elif prediction.decision == LoanDecision.CONDITIONAL:
            # For CONDITIONAL decisions, use approval probability if available
            if hasattr(prediction, 'approval_probability') and prediction.approval_probability is not None:
                return prediction.approval_probability >= conditional_threshold
            else:
                # If no probability available, default to DENY for safety
                print("No probability available, default to DENY for safety")
                return False
        else:
            # Unknown decision type, default to DENY for safety
            print("Unknown decision type, default to DENY for safety")
            return False

    def evaluate_batch_predictions(self,
                                   predictions: List[LLMResponse],
                                   actual_outcomes: List[bool],
                                   approach_name: str,
                                   loan_ids: Optional[List[str]] = None) -> BatchEvaluationResult:
        """
        Evaluate a batch of predictions against ground truth.

        Args:
            predictions: List of LLM prediction responses
            actual_outcomes: List of actual loan outcomes
            approach_name: Name of the approach being evaluated
            loan_ids: Optional list of loan identifiers

        Returns:
            BatchEvaluationResult with comprehensive metrics
        """
        start_time = time.time()

        if len(predictions) != len(actual_outcomes):
            raise ValueError(
                "Number of predictions must match number of actual outcomes")

        if loan_ids and len(loan_ids) != len(predictions):
            raise ValueError(
                "Number of loan IDs must match number of predictions")

        # Evaluate individual predictions
        individual_results = []
        for i, (prediction, actual) in enumerate(zip(predictions, actual_outcomes)):
            loan_id = loan_ids[i] if loan_ids else None
            result = self.evaluate_single_prediction(
                prediction, actual, loan_id)
            individual_results.append(result)

        # Calculate aggregate metrics using the new conversion logic
        predicted_approvals = [self._convert_decision_to_boolean(
            pred) for pred in predictions]

        # Basic metrics
        accuracy = accuracy_score(actual_outcomes, predicted_approvals)
        precision = precision_score(
            actual_outcomes, predicted_approvals, zero_division=0)
        recall = recall_score(
            actual_outcomes, predicted_approvals, zero_division=0)
        f1 = f1_score(actual_outcomes, predicted_approvals, zero_division=0)

        # Confusion matrix
        conf_mtrx = confusion_matrix(
            actual_outcomes, predicted_approvals).ravel()
        print("conf_matrx: ")
        print(conf_mtrx)

        # Handle single-class scenarios
        if len(conf_mtrx) == 1:
            # Only one class present in the data
            unique_actual = set(actual_outcomes)
            unique_predicted = set(predicted_approvals)

            if len(unique_actual) == 1 and len(unique_predicted) == 1:
                # Both actual and predicted have only one class
                if list(unique_actual)[0] == True and list(unique_predicted)[0] == True:
                    # All actual=True, all predicted=True
                    tn, fp, fn, tp = 0, 0, 0, conf_mtrx[0]
                elif list(unique_actual)[0] == False and list(unique_predicted)[0] == False:
                    # All actual=False, all predicted=False
                    tn, fp, fn, tp = conf_mtrx[0], 0, 0, 0
                elif list(unique_actual)[0] == True and list(unique_predicted)[0] == False:
                    # All actual=True, all predicted=False
                    tn, fp, fn, tp = 0, 0, conf_mtrx[0], 0
                else:
                    # All actual=False, all predicted=True
                    tn, fp, fn, tp = 0, conf_mtrx[0], 0, 0
            else:
                # Mixed case - shouldn't happen with len=1, but handle gracefully
                tn, fp, fn, tp = 0, 0, 0, conf_mtrx[0]
        elif len(conf_mtrx) == 4:
            # Standard 2x2 confusion matrix
            tn, fp, fn, tp = conf_mtrx
        else:
            # Unexpected case - handle gracefully
            print(f"Warning: Unexpected confusion matrix shape: {conf_mtrx}")
            tn, fp, fn, tp = 0, 0, 0, 0

        # Count correct predictions
        correct_predictions = sum(
            1 for result in individual_results if result.is_correct)
        total_predictions = len(predictions)

        evaluation_duration = time.time() - start_time

        return BatchEvaluationResult(
            total_predictions=total_predictions,
            correct_predictions=correct_predictions,
            accuracy=accuracy,
            true_positives=int(tp),
            true_negatives=int(tn),
            false_positives=int(fp),
            false_negatives=int(fn),
            precision=precision,
            recall=recall,
            f1_score=f1,
            approach_name=approach_name,
            individual_results=individual_results,
            evaluation_duration=evaluation_duration
        )

    def compare_approaches(self,
                           batch_results: List[BatchEvaluationResult],
                           primary_metric: str = "f1_score") -> ComparisonResult:
        """
        Compare multiple approaches and rank them.

        Args:
            batch_results: List of batch evaluation results
            primary_metric: Primary metric for ranking (accuracy, precision, recall, f1_score)

        Returns:
            ComparisonResult with rankings and comparisons
        """
        if not batch_results:
            raise ValueError("No batch results provided for comparison")

        # Create results dictionary
        approach_results = {
            result.approach_name: result for result in batch_results}

        # Rank approaches by different metrics
        metrics = ["accuracy", "precision", "recall", "f1_score"]
        rankings = {}

        for metric in metrics:
            metric_values = [(name, getattr(result, metric))
                             for name, result in approach_results.items()]
            metric_values.sort(key=lambda x: x[1], reverse=True)
            rankings[f"{metric}_ranking"] = [name for name, _ in metric_values]

        # Determine best approach based on primary metric
        primary_values = [(name, getattr(result, primary_metric))
                          for name, result in approach_results.items()]
        best_approach = max(primary_values, key=lambda x: x[1])[0]

        return ComparisonResult(
            approach_results=approach_results,
            best_approach=best_approach,
            best_metric=primary_metric,
            accuracy_ranking=rankings["accuracy_ranking"],
            precision_ranking=rankings["precision_ranking"],
            recall_ranking=rankings["recall_ranking"],
            f1_ranking=rankings["f1_score_ranking"]
        )

    def calculate_advanced_metrics(self,
                                   predictions: List[LLMResponse],
                                   actual_outcomes: List[bool]) -> Dict[str, float]:
        """
        Calculate advanced evaluation metrics.

        Args:
            predictions: List of LLM prediction responses
            actual_outcomes: List of actual loan outcomes

        Returns:
            Dictionary with advanced metrics
        """
        predicted_approvals = [self._convert_decision_to_boolean(
            pred) for pred in predictions]

        # Get probability scores if available
        approval_probs = []
        for pred in predictions:
            if pred.approval_probability is not None:
                approval_probs.append(pred.approval_probability)
            else:
                # Use confidence as proxy
                confidence_to_prob = {
                    "VERY_LOW": 0.1,
                    "LOW": 0.3,
                    "MEDIUM": 0.5,
                    "HIGH": 0.7,
                    "VERY_HIGH": 0.9
                }
                prob = confidence_to_prob.get(pred.confidence.value, 0.5)
                if pred.decision == LoanDecision.DENY:
                    prob = 1.0 - prob
                approval_probs.append(prob)

        metrics = {}

        # ROC AUC if we have probability scores
        if approval_probs:
            try:
                # Check if we have both classes
                unique_outcomes = set(actual_outcomes)
                if len(unique_outcomes) > 1:
                    metrics["roc_auc"] = roc_auc_score(
                        actual_outcomes, approval_probs)
                else:
                    # Cannot calculate with only one class
                    metrics["roc_auc"] = None
            except ValueError:
                metrics["roc_auc"] = None  # Cannot calculate ROC AUC

        # Business metrics
        conf_mtrx = confusion_matrix(
            actual_outcomes, predicted_approvals).ravel()

        # Handle single-class scenarios
        if len(conf_mtrx) == 1:
            # Only one class present in the data
            unique_actual = set(actual_outcomes)
            unique_predicted = set(predicted_approvals)

            if len(unique_actual) == 1 and len(unique_predicted) == 1:
                # Both actual and predicted have only one class
                if list(unique_actual)[0] == True and list(unique_predicted)[0] == True:
                    # All actual=True, all predicted=True
                    tn, fp, fn, tp = 0, 0, 0, conf_mtrx[0]
                elif list(unique_actual)[0] == False and list(unique_predicted)[0] == False:
                    # All actual=False, all predicted=False
                    tn, fp, fn, tp = conf_mtrx[0], 0, 0, 0
                elif list(unique_actual)[0] == True and list(unique_predicted)[0] == False:
                    # All actual=True, all predicted=False
                    tn, fp, fn, tp = 0, 0, conf_mtrx[0], 0
                else:
                    # All actual=False, all predicted=True
                    tn, fp, fn, tp = 0, conf_mtrx[0], 0, 0
            else:
                # Mixed case - shouldn't happen with len=1, but handle gracefully
                tn, fp, fn, tp = 0, 0, 0, conf_mtrx[0]
        elif len(conf_mtrx) == 4:
            # Standard 2x2 confusion matrix
            tn, fp, fn, tp = conf_mtrx
        else:
            # Unexpected case - handle gracefully
            tn, fp, fn, tp = 0, 0, 0, 0

        # False Positive Rate (approving bad loans)
        metrics["false_positive_rate"] = fp / (fp + tn) if (fp + tn) > 0 else 0

        # False Negative Rate (denying good loans)
        metrics["false_negative_rate"] = fn / (fn + tp) if (fn + tp) > 0 else 0

        # Specificity (correctly identifying bad loans)
        metrics["specificity"] = tn / (tn + fp) if (tn + fp) > 0 else 0

        # Negative Predictive Value
        metrics["negative_predictive_value"] = tn / \
            (tn + fn) if (tn + fn) > 0 else 0

        # Matthews Correlation Coefficient
        denominator = np.sqrt((tp + fp) * (tp + fn) * (tn + fp) * (tn + fn))
        if denominator != 0:
            metrics["matthews_correlation"] = (tp * tn - fp * fn) / denominator
        else:
            metrics["matthews_correlation"] = 0

        return metrics

    def analyze_confidence_calibration(self,
                                       predictions: List[LLMResponse],
                                       actual_outcomes: List[bool]) -> Dict[str, Any]:
        """
        Analyze how well-calibrated the confidence levels are.

        Args:
            predictions: List of LLM prediction responses
            actual_outcomes: List of actual loan outcomes

        Returns:
            Dictionary with calibration analysis
        """
        confidence_analysis = {}

        # Group by confidence level
        confidence_groups = {}
        for pred, actual in zip(predictions, actual_outcomes):
            conf_level = pred.confidence.value
            if conf_level not in confidence_groups:
                confidence_groups[conf_level] = {"correct": 0, "total": 0}

            confidence_groups[conf_level]["total"] += 1
            if (pred.decision == LoanDecision.APPROVE) == actual:
                confidence_groups[conf_level]["correct"] += 1

        # Calculate accuracy for each confidence level
        for conf_level, data in confidence_groups.items():
            accuracy = data["correct"] / \
                data["total"] if data["total"] > 0 else 0
            confidence_analysis[conf_level] = {
                "accuracy": accuracy,
                "count": data["total"],
                "correct": data["correct"]
            }

        return confidence_analysis

    def generate_evaluation_report(self,
                                   batch_result: BatchEvaluationResult,
                                   advanced_metrics: Optional[Dict[str,
                                                                   float]] = None,
                                   confidence_analysis: Optional[Dict[str, Any]] = None,
                                   loan_data: Optional[List] = None,
                                   predictions: Optional[List] = None) -> str:
        """
        Generate a comprehensive evaluation report.

        Args:
            batch_result: Batch evaluation result
            advanced_metrics: Optional advanced metrics
            confidence_analysis: Optional confidence calibration analysis
            loan_data: Optional list of original loan data for detailed reporting
            predictions: Optional list of LLM predictions for detailed reporting

        Returns:
            Formatted evaluation report
        """
        report = f"""
# Loan Approval Prediction Evaluation Report

## Approach: {batch_result.approach_name}
**Evaluation Date:** {batch_result.evaluation_timestamp.strftime('%Y-%m-%d %H:%M:%S')}
**Processing Time:** {batch_result.evaluation_duration:.2f} seconds

## Summary Metrics
- **Total Predictions:** {batch_result.total_predictions}
- **Correct Predictions:** {batch_result.correct_predictions}
- **Overall Accuracy:** {batch_result.accuracy:.3f} ({batch_result.accuracy*100:.1f}%)

## Performance Metrics
- **Precision:** {batch_result.precision:.3f}
- **Recall:** {batch_result.recall:.3f}
- **F1 Score:** {batch_result.f1_score:.3f}

## Confusion Matrix
|                | Predicted Deny | Predicted Approve |
|----------------|----------------|-------------------|
| **Actual Bad** | {batch_result.true_negatives:,} (TN)      | {batch_result.false_positives:,} (FP)       |
| **Actual Good**| {batch_result.false_negatives:,} (FN)      | {batch_result.true_positives:,} (TP)        |

## Business Impact
"""

        # Calculate business impact metrics safely
        # False Positive Rate = FP / (FP + TN) - Bad loans that were approved
        fpr_denominator = batch_result.false_positives + batch_result.true_negatives
        if fpr_denominator > 0:
            fpr = (batch_result.false_positives / fpr_denominator) * 100
            report += f"- **False Positive Rate:** {fpr:.1f}% (Bad loans approved)\n"
        else:
            report += f"- **False Positive Rate:** N/A (No bad loans in dataset)\n"

        # False Negative Rate = FN / (FN + TP) - Good loans that were denied
        fnr_denominator = batch_result.false_negatives + batch_result.true_positives
        if fnr_denominator > 0:
            fnr = (batch_result.false_negatives / fnr_denominator) * 100
            report += f"- **False Negative Rate:** {fnr:.1f}% (Good loans denied)\n"
        else:
            report += f"- **False Negative Rate:** N/A (No good loans in dataset)\n"

        if advanced_metrics:
            report += "\n## Advanced Metrics\n"

            # ROC AUC
            roc_auc = advanced_metrics.get('roc_auc')
            if roc_auc is not None and not (isinstance(roc_auc, str) and roc_auc == 'N/A'):
                report += f"- **ROC AUC:** {roc_auc:.3f}\n"
            else:
                report += f"- **ROC AUC:** N/A (Only one class present)\n"

            # Specificity
            specificity = advanced_metrics.get('specificity')
            if specificity is not None and not (isinstance(specificity, str) and specificity == 'N/A'):
                report += f"- **Specificity:** {specificity:.3f}\n"
            else:
                report += f"- **Specificity:** N/A\n"

            # Matthews Correlation
            matthews = advanced_metrics.get('matthews_correlation')
            if matthews is not None and not (isinstance(matthews, str) and matthews == 'N/A'):
                report += f"- **Matthews Correlation:** {matthews:.3f}\n"
            else:
                report += f"- **Matthews Correlation:** N/A\n"

        if confidence_analysis:
            report += "\n## Confidence Calibration\n"
            for conf_level, data in confidence_analysis.items():
                report += f"- **{conf_level}:** {data['accuracy']:.3f} accuracy ({data['correct']}/{data['count']} correct)\n"

        # Add detailed individual test case analysis
        if batch_result.individual_results and len(batch_result.individual_results) > 0:
            report += "\n## Detailed Test Case Analysis\n"

            for i, result in enumerate(batch_result.individual_results, 1):
                report += f"\n### Test Case {i}\n"

                # Basic prediction info
                report += f"**Prediction:** {result.predicted_decision.value}\n"
                report += f"**Actual Outcome:** {'Good Loan' if result.actual_outcome else 'Bad Loan'}\n"
                report += f"**Result:** {'CORRECT' if result.is_correct else 'INCORRECT'} ({result.prediction_type})\n"
                report += f"**Confidence:** {result.confidence_level.value}\n"
                report += f"**Risk Assessment:** {result.risk_level.value}\n"

                if result.loan_id:
                    report += f"**Loan ID:** {result.loan_id}\n"

                # Add loan data details if available
                if loan_data and i <= len(loan_data):
                    loan = loan_data[i-1]
                    report += f"\n**Loan Features:**\n"
                    report += f"- Loan Amount: ${loan.loan_amnt:,.2f}\n"
                    report += f"- Interest Rate: {loan.int_rate:.2f}%\n"
                    report += f"- Grade: {loan.grade.value}"
                    if loan.sub_grade:
                        report += f" ({loan.sub_grade})"
                    report += f"\n"
                    report += f"- Annual Income: ${loan.annual_inc:,.2f}\n"
                    report += f"- Debt-to-Income Ratio: {loan.dti:.2f}%\n"
                    report += f"- Home Ownership: {loan.home_ownership.value}\n"
                    report += f"- Employment Length: {loan.emp_length or 'Not specified'}\n"
                    report += f"- Purpose: {loan.purpose.value.replace('_', ' ').title()}\n"

                    if loan.fico_range_low and loan.fico_range_high:
                        report += f"- FICO Score: {loan.fico_range_low}-{loan.fico_range_high}"
                        if loan.fico_avg:
                            report += f" (Avg: {loan.fico_avg:.0f})"
                        report += f"\n"

                    if loan.revol_util is not None:
                        report += f"- Credit Utilization: {loan.revol_util:.1f}%\n"

                    report += f"- Target Variable: {loan.loan_status.value if loan.loan_status else 'Unknown'}\n"

                # Add LLM prediction details if available
                if predictions and i <= len(predictions):
                    pred = predictions[i-1]
                    report += f"\n**LLM Decision Analysis:**\n"
                    report += f"- **Decision:** {pred.decision.value}\n"

                    if hasattr(pred, 'approval_probability') and pred.approval_probability is not None:
                        report += f"- **Approval Probability:** {pred.approval_probability:.3f}\n"

                    if hasattr(pred, 'default_probability') and pred.default_probability is not None:
                        report += f"- **Default Probability:** {pred.default_probability:.3f}\n"

                    report += f"- **Reasoning:** {pred.reasoning}\n"

                    if pred.key_factors:
                        report += f"- **Key Factors:** {', '.join(pred.key_factors)}\n"

                    if pred.positive_factors:
                        report += f"- **Positive Factors:** {', '.join(pred.positive_factors)}\n"

                    if pred.negative_factors:
                        report += f"- **Negative Factors:** {', '.join(pred.negative_factors)}\n"

                    if pred.model_name:
                        report += f"- **Model Used:** {pred.model_name}\n"

                    if pred.processing_time:
                        report += f"- **Processing Time:** {pred.processing_time:.3f}s\n"

        return report.strip()


class BenchmarkSuite:
    """Suite for running comprehensive benchmarks."""

    def __init__(self, evaluator: LoanApprovalEvaluator):
        self.evaluator = evaluator
        self.benchmark_history = []
        self.detailed_reports = {}  # Store detailed reports for each approach

    def run_comprehensive_benchmark(self,
                                    approaches: List[Tuple[str, LoanApprovalApproach]],
                                    test_data: List[LoanData],
                                    ground_truth: List[bool]) -> ComparisonResult:
        """
        Run comprehensive benchmark across multiple approaches.

        Args:
            approaches: List of (name, approach) tuples
            test_data: List of loan data for testing
            ground_truth: List of actual loan outcomes

        Returns:
            ComparisonResult with comprehensive comparison
        """
        batch_results = []

        for approach_name, approach in approaches:
            print(f"Evaluating {approach_name}...")

            # Get predictions
            predictions = approach.predict_batch(test_data)

            # Evaluate predictions
            batch_result = self.evaluator.evaluate_batch_predictions(
                predictions, ground_truth, approach_name
            )

            batch_results.append(batch_result)

            # Calculate advanced metrics
            advanced_metrics = self.evaluator.calculate_advanced_metrics(
                predictions, ground_truth)

            # Analyze confidence calibration
            confidence_analysis = self.evaluator.analyze_confidence_calibration(
                predictions, ground_truth)

            # Generate report with detailed information
            report = self.evaluator.generate_evaluation_report(
                batch_result, advanced_metrics, confidence_analysis, test_data, predictions
            )

            # Store the detailed report
            self.detailed_reports[approach_name] = report

            print(f"Completed evaluation for {approach_name}")
            print(
                f"Accuracy: {batch_result.accuracy:.3f}, F1: {batch_result.f1_score:.3f}")

        # Compare approaches
        comparison_result = self.evaluator.compare_approaches(batch_results)

        # Store benchmark history
        self.benchmark_history.append({
            "timestamp": datetime.now(),
            "comparison_result": comparison_result,
            "test_size": len(test_data)
        })

        return comparison_result

    def get_detailed_reports(self) -> Dict[str, str]:
        """Get the detailed reports for each approach."""
        return self.detailed_reports.copy()

    def get_benchmark_summary(self) -> Dict[str, Any]:
        """Get summary of all benchmarks run."""
        if not self.benchmark_history:
            return {"message": "No benchmarks run yet"}

        latest_benchmark = self.benchmark_history[-1]
        comparison = latest_benchmark["comparison_result"]

        return {
            "total_benchmarks": len(self.benchmark_history),
            "latest_benchmark": {
                "timestamp": latest_benchmark["timestamp"],
                "test_size": latest_benchmark["test_size"],
                "best_approach": comparison.best_approach,
                "approaches_tested": list(comparison.approach_results.keys()),
                "performance_summary": comparison.get_performance_summary()
            }
        }
