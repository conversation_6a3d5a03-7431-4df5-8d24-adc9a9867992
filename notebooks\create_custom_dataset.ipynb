import pandas as pd
import pathlib as pl

# Make sure jupyter lab's current directory is the project root
DATA_PATH = pl.Path.cwd() / "data" / "Lending Club loan data" / "loan.csv"


chunk_size = 10000
chunks = []

for chunk in pd.read_csv(DATA_PATH, chunksize=chunk_size):
    chunks.append(chunk)

df = pd.concat(chunks, ignore_index=True)

df.shape

df["loan_status"].value_counts()

df_default = df[df["loan_status"] == "Default"]
df_default.shape

nb_default = df_default.shape[0]
nb_default

df_paid = df[df["loan_status"] == "Fully Paid"][:nb_default]
df_paid.shape

df_balanced = pd.concat([df_default, df_paid], ignore_index=True)
df_balanced.shape

df_balanced.to_csv("data/Lending Club loan data/loan_d_fp_balanced.csv", index=False)

variables = ["loan_amnt", "int_rate", "grade", "sub_grade", "annual_inc", "dti", "emp_length", "home_ownership", "purpose", "loan_status"]

for var in variables:
    try:
        df_balanced[var]
        print(f"Variable {var} found in dataframe")
    except KeyError:
        print(f"Variable {var} not found in dataframe")

df_balanced[variables].iloc[8]