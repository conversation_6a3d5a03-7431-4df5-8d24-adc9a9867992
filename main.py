#!/usr/bin/env python3
"""
Main script for loan approval prediction using LLM approaches.
"""
# Recommended usage:
# python main.py --csv-file "data/Lending Club loan data/loan_data_exported.csv" --max-records 3 --config config.yaml --approach single
from src.utils.langfuse_integration import LangfuseTracker
from src.evaluation.benchmarking import LoanApprovalEvaluator, BenchmarkSuite
from src.approaches.court_system import CourtSystemApproach
from src.approaches.poll import PoLLApproach
from src.approaches.single_frontier import SingleFrontierApproach
from src.providers.factory import LLMProviderFactory, ConfigManager, create_frontier_model, create_poll_ensemble, create_court_models
from src.utils.data_loader import LoanDataLoader
import os
import sys
import argparse
from pathlib import Path
from typing import List, Dict, Any
import json
from datetime import datetime
import warnings

# Suppress sklearn warnings for single-class datasets
warnings.filterwarnings(
    "ignore", message="Only one class is present in y_true")
warnings.filterwarnings("ignore", category=UserWarning, module="sklearn")

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))


def main():
    parser = argparse.ArgumentParser(
        description="Loan Approval Prediction with LLMs")
    parser.add_argument("--csv-file", required=True,
                        help="Path to loan data CSV file")
    parser.add_argument("--config", default="config.yaml",
                        help="Path to configuration file")
    parser.add_argument("--approach", choices=["single", "poll", "court", "all"], default="single",
                        help="Which approach to use")
    parser.add_argument("--max-records", type=int,
                        help="Maximum number of records to process")
    parser.add_argument("--sample-rate", type=float,
                        help="Sampling rate (0.0 to 1.0)")
    parser.add_argument("--output-dir", default="results",
                        help="Output directory for results")
    # Note: No train/test split needed for LLM evaluation
    parser.add_argument("--enable-langfuse",
                        action="store_true", help="Enable Langfuse tracking")

    args = parser.parse_args()

    # Create output directory
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)

    print("🏦 Loan Approval Prediction System")
    print("=" * 50)

    # Initialize components
    config_manager = ConfigManager(args.config)
    data_loader = LoanDataLoader()
    evaluator = LoanApprovalEvaluator()
    benchmark_suite = BenchmarkSuite(evaluator)

    # Initialize Langfuse if enabled
    langfuse_tracker = None
    if args.enable_langfuse:
        langfuse_config = config_manager.get_langfuse_config()
        langfuse_tracker = LangfuseTracker(
            project_name=langfuse_config.get(
                "project_name", "loan-approval-prediction"),
            enabled=langfuse_config.get("enabled", True),
            sample_rate=langfuse_config.get("trace_sample_rate", 1.0)
        )
        session_id = langfuse_tracker.create_session("loan_prediction_run")

    try:
        # Load data
        print(f"📊 Loading data from {args.csv_file}")
        loan_data = data_loader.load_csv(
            args.csv_file,
            max_records=args.max_records,
            sample_rate=args.sample_rate
        )

        if not loan_data:
            print("❌ No loan data loaded. Please check your CSV file.")
            return

        # Get data summary
        summary = data_loader.get_data_summary(loan_data)
        print(f"📈 Data Summary:")
        print(f"   Total loans: {summary['total_loans']:,}")
        print(f"   Loans with status: {summary['loans_with_status']:,}")
        print(f"   Good loan ratio: {summary.get('good_loan_ratio', 0):.3f}")

        # Prepare evaluation data (no train/test split needed for LLM evaluation)
        print(f"🔄 Preparing evaluation data")
        # Filter loans with known status for evaluation
        evaluation_data = [
            loan for loan in loan_data if loan.loan_status is not None]
        evaluation_labels = [loan.is_good_loan() for loan in evaluation_data]

        if not evaluation_data:
            print("❌ No loans with status information found for evaluation.")
            return

        print(f"📊 Evaluation set: {len(evaluation_data)} loans")
        print(
            f"   Good loan ratio: {sum(evaluation_labels)/len(evaluation_labels):.3f}")

        # Initialize approaches
        approaches = []

        if args.approach in ["single", "all"]:
            print("🤖 Initializing Single Frontier Model...")
            try:
                frontier_provider = create_frontier_model(args.config)
                single_approach = SingleFrontierApproach(frontier_provider)
                approaches.append(("Single Frontier", single_approach))
                print(f"   ✅ Using {frontier_provider.model_name}")
            except Exception as e:
                print(f"   ❌ Failed to initialize Single Frontier: {e}")

        if args.approach in ["poll", "all"]:
            print("🏛️ Initializing PoLL Ensemble...")
            try:
                poll_ensemble = create_poll_ensemble(args.config)
                poll_approach = PoLLApproach(poll_ensemble)
                approaches.append(("PoLL Ensemble", poll_approach))
                print(f"   ✅ Using {len(poll_ensemble.providers)} models")
            except Exception as e:
                print(f"   ❌ Failed to initialize PoLL: {e}")

        if args.approach in ["court", "all"]:
            print("⚖️ Initializing Court System...")
            try:
                court_models = create_court_models(args.config)
                court_approach = CourtSystemApproach(
                    court_models['pros_model'],
                    court_models['cons_model'],
                    court_models['judge_model']
                )
                approaches.append(("Court System", court_approach))
                print(f"   ✅ Using 3 models for deliberation")
            except Exception as e:
                print(f"   ❌ Failed to initialize Court System: {e}")

        if not approaches:
            print(
                "❌ No approaches successfully initialized. Please check your configuration.")
            return

        # Run benchmark
        print(
            f"🧪 Running benchmark on {len(evaluation_data)} evaluation samples...")
        comparison_result = benchmark_suite.run_comprehensive_benchmark(
            approaches, evaluation_data, evaluation_labels
        )

        # Track with Langfuse if enabled
        if langfuse_tracker:
            langfuse_tracker.track_approach_comparison(
                comparison_result, session_id)

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = output_dir / f"benchmark_results_{timestamp}.json"

        results_data = {
            "timestamp": timestamp,
            "config_file": args.config,
            "csv_file": args.csv_file,
            "evaluation_samples": len(evaluation_data),
            "best_approach": comparison_result.best_approach,
            "performance_summary": comparison_result.get_performance_summary(),
            "rankings": {
                "accuracy": comparison_result.accuracy_ranking,
                "precision": comparison_result.precision_ranking,
                "recall": comparison_result.recall_ranking,
                "f1_score": comparison_result.f1_ranking
            }
        }

        with open(results_file, 'w') as f:
            json.dump(results_data, f, indent=2, default=str)

        # Print results
        print("\n🏆 BENCHMARK RESULTS")
        print("=" * 50)
        print(f"Best Approach: {comparison_result.best_approach}")
        print(f"Best Metric: {comparison_result.best_metric}")
        print("\nPerformance Summary:")

        for approach, metrics in comparison_result.get_performance_summary().items():
            print(f"\n{approach}:")
            print(f"  Accuracy:  {metrics['accuracy']:.3f}")
            print(f"  Precision: {metrics['precision']:.3f}")
            print(f"  Recall:    {metrics['recall']:.3f}")
            print(f"  F1 Score:  {metrics['f1_score']:.3f}")

        print(f"\n📁 Results saved to: {results_file}")

        # Generate detailed reports using the enhanced reports from benchmark suite
        detailed_reports = benchmark_suite.get_detailed_reports()
        for approach_name in comparison_result.approach_results.keys():
            report_file = output_dir / \
                f"report_{approach_name.lower().replace(' ', '_')}_{timestamp}.md"

            # Use the detailed report if available, otherwise fall back to basic report
            if approach_name in detailed_reports:
                report = detailed_reports[approach_name]
            else:
                # Fallback to basic report generation
                batch_result = comparison_result.approach_results[approach_name]
                report = evaluator.generate_evaluation_report(batch_result)

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            print(f"📄 {approach_name} report: {report_file}")

        print("\n✅ Benchmark completed successfully!")

    except Exception as e:
        print(f"❌ Error during execution: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Cleanup
        if langfuse_tracker:
            langfuse_tracker.flush()

        # Cleanup PoLL ensemble if used
        if 'approaches' in locals():
            for approach_name, approach in approaches:
                if hasattr(approach, 'cleanup'):
                    approach.cleanup()


if __name__ == "__main__":
    main()
